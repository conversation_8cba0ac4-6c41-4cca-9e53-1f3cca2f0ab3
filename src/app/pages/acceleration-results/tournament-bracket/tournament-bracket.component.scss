@import 'https://fonts.googleapis.com/css?family=Roboto+Slab:400,700';

html {
  font-size: 1rem;
}

body {
  background: #f0f2f2;
}

.bracket-container {
  padding: 2rem 1.5rem;
  margin: 1rem 0;
  background: #f8f9fa;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  //overflow-x: auto;
  min-height: 400px;
}

.bracket {
  display: inline-block;
  white-space: nowrap;
  font-size: 0;
  margin-left: 1rem;
  margin-top: 1rem;
}
.bracket .round {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  margin-right: 1rem;
}

.bracket .round .round-title {
  font-family: "Roboto Slab", serif;
  font-size: 1.1rem;
  font-weight: 700;
  color: #333;
  text-align: center;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background: #e9ecef;
  border-radius: 0.25rem;
  border: 2px solid #dee2e6;
  white-space: normal;
  min-width: 120px;
}
.bracket .round .winners > div {
  display: inline-block;
  vertical-align: middle;
}
.bracket .round .winners > div.matchups .matchup:last-child {
  margin-bottom: 0 !important;
}
.bracket .round .winners > div.matchups .matchup .participants {
  border-radius: 0.25rem;
  overflow: hidden;
}
.bracket .round .winners > div.matchups .matchup .participants .participant {
  box-sizing: border-box;
  color: #858585;
  border-left: 0.25rem solid #858585;
  background: white;
  width: 15rem;
  height: 3rem;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.12);
  transition: all 0.2s ease;
}

.bracket .round .winners > div.matchups .matchup .participants .participant.placeholder {
  background: #f8f9fa;
  color: #6c757d;
  border-left-color: #dee2e6;
  font-style: italic;
}

.bracket .round .winners > div.matchups .matchup .participants .participant.placeholder span {
  opacity: 0.7;
}
.bracket .round .winners > div.matchups .matchup .participants .participant.winner {
  color: #60c645;
  border-color: #60c645;
  background: #f8fff8;
  font-weight: 600;
}

.bracket .round .winners > div.matchups .matchup .participants .participant.loser {
  color: #dc563f;
  border-color: #dc563f;
  background: #fff8f8;
}
.bracket .round .winners > div.matchups .matchup .participants .participant:not(:last-child) {
  border-bottom: thin solid #f0f2f2;
}
.bracket .round .winners > div.matchups .matchup .participants .participant span {
  margin: 0 1.25rem;
  line-height: 3;
  font-size: 1rem;
  font-family: "Roboto Slab";
}
.bracket .round .winners > div.connector.filled .line, .bracket .round .winners > div.connector.filled.bottom .merger:after, .bracket .round .winners > div.connector.filled.top .merger:before {
  border-color: #60c645;
}
.bracket .round .winners > div.connector .line, .bracket .round .winners > div.connector .merger {
  box-sizing: border-box;
  width: 2rem;
  display: inline-block;
  vertical-align: top;
}
.bracket .round .winners > div.connector .line {
  border-bottom: thin solid #c0c0c8;
  height: 4rem;
}
.bracket .round .winners > div.connector .merger {
  position: relative;
  height: 8rem;
}
.bracket .round .winners > div.connector .merger:before, .bracket .round .winners > div.connector .merger:after {
  content: "";
  display: block;
  box-sizing: border-box;
  width: 100%;
  height: 50%;
  border: 0 solid;
  border-color: #c0c0c8;
}
.bracket .round .winners > div.connector .merger:before {
  border-right-width: thin;
  border-top-width: thin;
}
.bracket .round .winners > div.connector .merger:after {
  border-right-width: thin;
  border-bottom-width: thin;
}
.bracket .round.quarterfinals .winners:not(:last-child) {
  margin-bottom: 2rem;
}
.bracket .round.quarterfinals .winners .matchups .matchup:not(:last-child) {
  margin-bottom: 2rem;
}
.bracket .round.semifinals .winners .matchups .matchup:not(:last-child) {
  margin-bottom: 10rem;
}
.bracket .round.semifinals .winners .connector .merger {
  height: 16rem;
}
.bracket .round.semifinals .winners .connector .line {
  height: 8rem;
}
.bracket .round.finals .winners .connector .merger {
  height: 3rem;
}
.bracket .round.finals .winners .connector .line {
  height: 1.5rem;
}

// Responsive design improvements
@media (max-width: 768px) {
  .bracket-container {
    padding: 1rem 0.5rem;
    margin: 0.5rem 0;
  }

  .bracket {
    margin-left: 0.5rem;
    margin-top: 0.5rem;
  }

  .bracket .round .winners > div.matchups .matchup .participants .participant {
    width: 12rem;
    height: 2.5rem;
  }

  .bracket .round .winners > div.matchups .matchup .participants .participant span {
    margin: 0 1rem;
    line-height: 2.5;
    font-size: 0.9rem;
  }

  .bracket .round .round-title {
    font-size: 1rem;
    margin-bottom: 0.75rem;
    padding: 0.4rem;
    min-width: 100px;
  }
}

// Improved spacing for better visual hierarchy
.bracket .round:not(:last-child) {
  margin-right: 1.5rem;
}

// Enhanced connector styling
.bracket .round .winners > div.connector.filled .line,
.bracket .round .winners > div.connector.filled.bottom .merger:after,
.bracket .round .winners > div.connector.filled.top .merger:before {
  border-color: #60c645;
  border-width: 2px;
}

// Hover effects for better interactivity
.bracket .round .winners > div.matchups .matchup .participants .participant:not(.placeholder):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

// Loading state for pending matches
.bracket .round .winners > div.matchups .matchup .participants .participant.placeholder {
  position: relative;
  overflow: hidden;
}

.bracket .round .winners > div.matchups .matchup .participants .participant.placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

// Improved visual hierarchy for different rounds
.bracket .round.quarterfinals .round-title {
  background: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
}

.bracket .round.semifinals .round-title {
  background: #fff3e0;
  border-color: #ff9800;
  color: #f57c00;
}

.bracket .round.finals .round-title {
  background: #fce4ec;
  border-color: #e91e63;
  color: #c2185b;
}
