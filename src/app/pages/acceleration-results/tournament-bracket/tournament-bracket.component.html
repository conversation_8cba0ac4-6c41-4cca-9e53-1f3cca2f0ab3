<div class="bracket-container">
  <div class="bracket">
    <!-- First Round (Quarterfinals) - Show actual teams -->
    <section class="round quarterfinals" *ngIf="rounds.length > 0">
      <div class="round-title">{{ roundNames[0] || 'Round 1' }}</div>
      <div class="winners" *ngFor="let winnerGroup of getQuarterfinalGroups(); let groupIndex = index">
        <div class="matchups">
          <div class="matchup" *ngFor="let match of winnerGroup">
            <div class="participants">
              <div class="participant"
                   [class.winner]="isWinner(match, match.team1)"
                   *ngIf="match.team1">
                <span>{{ match.team1.rank }}- {{ getTeamShortName(match.team1) }}</span>
              </div>
              <div class="participant placeholder" *ngIf="!match.team1">
                <span>TBD</span>
              </div>

              <div class="participant"
                   [class.winner]="isWinner(match, match.team2)"
                   *ngIf="match.team2">
                <span>{{ match.team2.rank }}- {{ getTeamShortName(match.team2) }}</span>
              </div>
              <div class="participant placeholder" *ngIf="!match.team2">
                <span>TBD</span>
              </div>
            </div>
          </div>
        </div>
        <div class="connector" *ngIf="groupIndex < getQuarterfinalGroups().length - 1 || rounds.length > 1">
          <div class="merger"></div>
          <div class="line"></div>
        </div>
      </div>
    </section>

    <!-- Second Round (Semifinals) - Show actual teams -->
    <section class="round semifinals" *ngIf="rounds.length > 1">
      <div class="round-title">{{ roundNames[1] || 'Semi-Final' }}</div>
      <div class="winners">
        <div class="matchups">
          <div class="matchup" *ngFor="let match of rounds[1]">
            <div class="participants">
              <div class="participant"
                   [class.winner]="isWinner(match, match.team1)"
                   *ngIf="match.team1">
                <span>{{ match.team1.rank }}- {{ getTeamShortName(match.team1) }}</span>
              </div>
              <div class="participant placeholder" *ngIf="!match.team1">
                <span>Waiting...</span>
              </div>
              <div class="participant"
                   [class.winner]="isWinner(match, match.team2)"
                   *ngIf="match.team2">
                <span>{{ match.team2.rank }}- {{ getTeamShortName(match.team2) }}</span>
              </div>
              <div class="participant placeholder" *ngIf="!match.team2">
                <span>Waiting...</span>
              </div>
            </div>
          </div>
        </div>
        <div class="connector" *ngIf="rounds.length > 2">
          <div class="merger"></div>
          <div class="line"></div>
        </div>
      </div>
    </section>

    <!-- Third Round and beyond - Show placeholders -->
    <section class="round finals"
             [ngClass]="getRoundClass(roundIndex + 2)"
             *ngFor="let round of rounds.slice(2); let roundIndex = index">
      <div class="round-title">{{ roundNames[roundIndex + 2] || 'Round ' + (roundIndex + 3) }}</div>
      <div class="winners">
        <div class="matchups">
          <div class="matchup" *ngFor="let match of round">
            <div class="participants">

              <div class="participant"
                   [class.winner]="isWinner(match, match.team1)"
                   *ngIf="match.team1">
                <span>{{ match.team1.rank }}- {{ getTeamShortName(match.team1) }}</span>
              </div>
              <div class="participant placeholder" *ngIf="!match.team1">
                <span>Waiting...</span>
              </div>
              <div class="participant"
                   [class.winner]="isWinner(match, match.team2)"
                   *ngIf="match.team2">
                <span>{{ match.team2.rank }}- {{ getTeamShortName(match.team2) }}</span>
              </div>
              <div class="participant placeholder" *ngIf="!match.team2">
                <span>Waiting...</span>
              </div>


            </div>
          </div>
        </div>
        <div class="connector" *ngIf="roundIndex < rounds.slice(2).length - 1">
          <div class="merger"></div>
          <div class="line"></div>
        </div>
      </div>
    </section>
  </div>
</div>
