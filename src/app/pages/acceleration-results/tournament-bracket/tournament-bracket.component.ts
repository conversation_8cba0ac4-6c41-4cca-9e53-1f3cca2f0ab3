import { Component, Input, OnChanges, OnInit } from '@angular/core';

interface TournamentTeam {
  rank: number;
  logo: string;
  teamName: string;
  vehicleNo: number;
  universityName: string;
  time: string;
  winner: boolean;
}

interface BracketMatch {
  id: string;
  team1?: TournamentTeam;
  team2?: TournamentTeam;
  winner?: TournamentTeam;
  round: number;
  matchNumber: number;
}

@Component({
  selector: 'app-tournament-bracket',
  templateUrl: './tournament-bracket.component.html',
  styleUrls: ['./tournament-bracket.component.scss']
})
export class TournamentBracketComponent implements OnInit, OnChanges {
  @Input() teams: Record<string, TournamentTeam[]> = {
    fist: [],
    second: [],
    third: [],
  };

  rounds: BracketMatch[][] = [];
  roundNames: string[] = [];

  constructor() { }

  ngOnInit(): void {
    this.generateBracket();

  }


  generateBracket(): void {
    if (this.teams.first?.length == 0) {
      return;
    }

    // Filter out teams with null time (DNF teams)
    const qualifiedTeams = this.teams.first?.filter(team => team.time !== null);

    // Ensure we have a power of 2 number of teams for proper bracket
    const bracketSize = this.getNextPowerOfTwo(qualifiedTeams.length);
    const teamsToUse = qualifiedTeams.slice(0, bracketSize);

    this.rounds = [];
    this.generateRoundNames(bracketSize);

    // Generate first round
    const firstRound = this.generateFirstRound(teamsToUse);
    this.rounds.push(firstRound);


    const secondTeams = this.teams.second?.filter(team => team.time !== null);
    this.rounds.push(this.generateFirstRound(secondTeams));

    const thirdTeams = this.teams.third;
    this.rounds.push(this.generateFirstRound(thirdTeams));

  }

  private getNextPowerOfTwo(n: number): number {
    if (n <= 1) {
      return 2;
    }
    if (n <= 2) {
      return 2;
    }
    if (n <= 4) {
      return 4;
    }
    if (n <= 8) {
      return 8;
    }
    if (n <= 16) {
      return 16;
    }
    return 32;
  }

  private generateRoundNames(bracketSize: number): void {
    this.roundNames = [];
    let rounds = Math.log2(bracketSize);

    for (let i = 1; i <= rounds; i++) {
      if (i === rounds) {
        this.roundNames.push('Final');
      } else if (i === rounds - 1) {
        this.roundNames.push('Semi-Final');
      } else if (i === rounds - 2) {
        this.roundNames.push('Quarter-Final');
      } else {
        this.roundNames.push(`Round ${i}`);
      }
    }
  }

  private generateFirstRound(teams: TournamentTeam[]): BracketMatch[] {
    const matches: BracketMatch[] = [];

    for (let i = 0; i < teams.length; i += 2) {
      const team1 = teams[i];
      const team2 = teams[i + 1] || null;

      const match: BracketMatch = {
        id: `round1-match${i / 2 + 1}`,
        team1: team1,
        team2: team2,
        round: 1,
        matchNumber: i / 2 + 1
      };

      // Determine winner automatically
      match.winner = this.getMatchWinner(team1, team2);

      matches.push(match);
    }

    return matches;
  }

  private generateNextRound(previousRound: BracketMatch[], roundNumber: number): BracketMatch[] {
    const matches: BracketMatch[] = [];

    for (let i = 0; i < previousRound.length; i += 2) {
      const team1 = previousRound[i]?.winner;
      const team2 = previousRound[i + 1]?.winner;

      const match: BracketMatch = {
        id: `round${roundNumber}-match${i / 2 + 1}`,
        team1: team1 || null,
        team2: team2 || null,
        round: roundNumber,
        matchNumber: i / 2 + 1
      };

      // Determine winner for this round
      match.winner = this.getMatchWinner(team1, team2);

      matches.push(match);
    }

    return matches;
  }

  getTeamDisplayName(team: TournamentTeam): string {
    return `#${team.vehicleNo} ${team.teamName}`;
  }

  getTeamShortName(team: TournamentTeam): string {
    return team.teamName + ' #' + team.vehicleNo;
  }

  isWinner(match: BracketMatch, team: TournamentTeam): boolean {
    return match.winner && team && match.winner.vehicleNo === team.vehicleNo;
  }

  getMatchWinner(team1: TournamentTeam, team2: TournamentTeam): TournamentTeam | null {
    if (team1.winner) {
      return team1;
    }
    if (team2.winner) {
      return team2;
    }
    return null;
  }

  getQuarterfinalGroups(): BracketMatch[][] {
    if (this.rounds.length === 0) {
      return [];
    }

    const firstRound = this.rounds[0];
    const groups: BracketMatch[][] = [];

    // Group matches in pairs for proper bracket display
    for (let i = 0; i < firstRound.length; i += 2) {
      const group = [firstRound[i]];
      if (firstRound[i + 1]) {
        group.push(firstRound[i + 1]);
      }
      groups.push(group);
    }

    return groups;
  }

  getRoundClass(roundNumber: number): string {
    switch (roundNumber) {
      case 1:
        return 'quarterfinals';
      case 2:
        return 'semifinals';
      case 3:
        return 'finals';
      default:
        return 'finals';
    }
  }
}
