import { Component, OnInit } from '@angular/core';

interface AccelerationResult {
  rank: number;
  logo: string;
  teamName: string;
  vehicleNo: number;
  universityName: string;
  time: string; // saniye.salise formatında
}

@Component({
  selector: 'app-acceleration-results',
  templateUrl: './acceleration-results.component.html',
  styleUrls: ['./acceleration-results.component.scss']
})
export class AccelerationResultsComponent implements OnInit {

  // bread crumb items
  breadCrumbItems: Array<{}>;

  selectedCategory: string = '2025-ec-elektromobil';

  categories = [
    { value: '2025-ec-elektromobil', label: '2025 EC Uluslararası İvmelenme' },
    { value: '2025-ec-lise', label: '2025 EC Lise İvmelenme' }
  ];

  // Statik veriler
  elektromobilData: AccelerationResult[] = [
    {
      rank: 1,
      logo: 'http://localhost:8000/storage/assets/2025/08/23/f1792749aa71d81418709a03aaf5b271_thumbnail.jpg',
      teamName: 'YTU AESK E',
      vehicleNo: 3,
      universityName: 'YILDIZ TEKNİK ÜNİVERSİTESİ',
      time: '19.31'
    },
    {
      rank: 2,
      logo: 'http://localhost:8000/storage/assets/2025/08/23/908ca2bbf1338112fbb81542ac7f4217_thumbnail.jpg',
      teamName: '1.5 ADANA ELEKTRO',
      vehicleNo: 21,
      universityName: 'ÇUKUROVA ÜNİVERSİTESİ',
      time: '25.41'
    },
    {
      rank: 3,
      logo: 'http://localhost:8000/storage/assets/2025/08/23/a20380a54ce1d3d27b426f717cfa898c_thumbnail.jpg',
      teamName: 'SUBÜ TETRA-EMT',
      vehicleNo: 2,
      universityName: 'SAKARYA UYGULAMALI BİLİMLER ÜNİVERSİTESİ',
      time: '25.97'
    },
    {
      rank: 4,
      logo: null,
      teamName: 'Mavera TT ŞSÇGM',
      vehicleNo: 42,
      universityName: 'ESKİŞEHİR OSMANGAZİ ÜNİVERSİTESİ',
      time: '27.78'
    },
    {
      rank: 5,
      logo: 'http://localhost:8000/storage/assets/2025/08/23/bfb957e7ed74cc13e1c2070ef87d7964_thumbnail.jpg',
      teamName: 'SAITEM',
      vehicleNo: 66,
      universityName: 'SAKARYA ÜNİVERSİTESİ',
      time: '27.34'
    },
    {
      rank: 6,
      logo: 'http://localhost:8000/storage/assets/2025/08/23/37c1e6fe42dfc04c961ba52e61225d32_thumbnail.jpg',
      teamName: 'EVOART-E',
      vehicleNo: 46,
      universityName: 'SAMSUN ÜNİVERSİTESİ',
      time: '27.68'
    },
    {
      rank: '-',
      logo: 'http://localhost:8000/storage/assets/2025/08/23/a5969c521ab8b69d1ef15542452db18b_thumbnail.jpg',
      teamName: 'DUSCART',
      vehicleNo: 14,
      universityName: 'KÜTAHYA DUMLUPINAR ÜNİVERSİTESİ',
      time: '-'
    },
    {
      rank: '-',
      logo: 'http://localhost:8000/storage/assets/2025/08/23/33519db7948955383690c2dc5a66e3b1_thumbnail.jpg',
      teamName: 'Team Efe',
      vehicleNo: 9,
      universityName: 'İZMİR KATİP ÇELEBİ ÜNİVERSİTESİ',
      time: '-'
    },
    {
      rank: '-',
      logo: 'http://localhost:8000/storage/assets/2025/08/23/a5969c521ab8b69d1ef15542452db18b_thumbnail.jpg',
      teamName: '1.5 Adana AGM Hidro',
      vehicleNo: 6,
      universityName: 'ÇUKUROVA ÜNİVERSİTESİ',
      time: '-'
    },
    {
      rank: '-',
      logo: 'http://localhost:8000/storage/assets/2025/08/23/33519db7948955383690c2dc5a66e3b1_thumbnail.jpg',
      teamName: 'YTU AESK H',
      vehicleNo: 77,
      universityName: 'YILDIZ TEKNİK ÜNİVERSİTESİ',
      time: '-'
    }
  ];
  elektromobilOrder: Record<string, AccelerationResult[]> = {
    first: [
      {
        rank: '1',
        logo: 'http://localhost:8000/storage/assets/2025/08/23/f1792749aa71d81418709a03aaf5b271_thumbnail.jpg',
        teamName: 'YTU AESK E',
        vehicleNo: 3,
        universityName: 'YILDIZ TEKNİK ÜNİVERSİTESİ',
        time: '19.31',
        winner: true
      },
      {
        rank: '8',
        logo: 'http://localhost:8000/storage/assets/2025/08/23/33519db7948955383690c2dc5a66e3b1_thumbnail.jpg',
        teamName: 'YTU AESK H',
        vehicleNo: 77,
        universityName: 'YILDIZ TEKNİK ÜNİVERSİTESİ',
        time: '-'
      },

      {
        rank: 4,
        logo: null,
        teamName: 'Mavera TT ŞSÇGM',
        vehicleNo: 42,
        universityName: 'ESKİŞEHİR OSMANGAZİ ÜNİVERSİTESİ',
        time: '27.78',
        winner: true

      },
      {
        rank: 5,
        logo: 'http://localhost:8000/storage/assets/2025/08/23/bfb957e7ed74cc13e1c2070ef87d7964_thumbnail.jpg',
        teamName: 'SAITEM',
        vehicleNo: 66,
        universityName: 'SAKARYA ÜNİVERSİTESİ',
        time: '27.34'
      },


      {
        rank: 2,
        logo: 'http://localhost:8000/storage/assets/2025/08/23/908ca2bbf1338112fbb81542ac7f4217_thumbnail.jpg',
        teamName: '1.5 ADANA ELEKTRO',
        vehicleNo: 21,
        universityName: 'ÇUKUROVA ÜNİVERSİTESİ',
        time: '25.41',
        winner: true

      },
      {
        rank: '7',
        logo: 'http://localhost:8000/storage/assets/2025/08/23/a5969c521ab8b69d1ef15542452db18b_thumbnail.jpg',
        teamName: '1.5 Adana AGM Hidro',
        vehicleNo: 6,
        universityName: 'ÇUKUROVA ÜNİVERSİTESİ',
        time: '-'
      },

      {
        rank: 3,
        logo: 'http://localhost:8000/storage/assets/2025/08/23/a20380a54ce1d3d27b426f717cfa898c_thumbnail.jpg',
        teamName: 'SUBÜ TETRA-EMT',
        vehicleNo: 2,
        universityName: 'SAKARYA UYGULAMALI BİLİMLER ÜNİVERSİTESİ',
        time: '25.97',
        winner: true

      },

      {
        rank: 6,
        logo: 'http://localhost:8000/storage/assets/2025/08/23/37c1e6fe42dfc04c961ba52e61225d32_thumbnail.jpg',
        teamName: 'EVOART-E',
        vehicleNo: 46,
        universityName: 'SAMSUN ÜNİVERSİTESİ',
        time: '27.68'
      },
    ],
    second: [
      {
        rank: 1,
        logo: 'http://localhost:8000/storage/assets/2025/08/23/f1792749aa71d81418709a03aaf5b271_thumbnail.jpg',
        teamName: 'YTU AESK E',
        vehicleNo: 3,
        universityName: 'YILDIZ TEKNİK ÜNİVERSİTESİ',
        time: '19.31'
      },
      {
        rank: 4,
        logo: null,
        teamName: 'Mavera TT ŞSÇGM',
        vehicleNo: 42,
        universityName: 'ESKİŞEHİR OSMANGAZİ ÜNİVERSİTESİ',
        time: '27.78'
      },


      {
        rank: 2,
        logo: 'http://localhost:8000/storage/assets/2025/08/23/908ca2bbf1338112fbb81542ac7f4217_thumbnail.jpg',
        teamName: '1.5 ADANA ELEKTRO',
        vehicleNo: 21,
        universityName: 'ÇUKUROVA ÜNİVERSİTESİ',
        time: '25.41'
      },

      {
        rank: 3,
        logo: 'http://localhost:8000/storage/assets/2025/08/23/a20380a54ce1d3d27b426f717cfa898c_thumbnail.jpg',
        teamName: 'SUBÜ TETRA-EMT',
        vehicleNo: 2,
        universityName: 'SAKARYA UYGULAMALI BİLİMLER ÜNİVERSİTESİ',
        time: '25.97'
      },


    ],
    third: [
      {
        rank: null,
        logo: '',
        teamName: 'Waiting',
        vehicleNo: '',
        universityName: '',
        time: '1.1'
      },

      {
        rank: null,
        logo: '',
        teamName: 'Waiting',
        vehicleNo: '',
        universityName: '',
        time: '1.1'
      },

    ]
  };

  hidromobilData: AccelerationResult[] = [];

  liseData: AccelerationResult[] = [];
  liseOrder: Record<string, AccelerationResult[]> = {
    first: [
    ],
    second: [
    ],
    third: [

    ]
  };


  currentResults: AccelerationResult[] = [];
  currentOrder: Record<string, AccelerationResult[]>;

  constructor() { }

  ngOnInit() {
    this.breadCrumbItems = [{ label: 'İvmelenme Sonuçları' }];
    this.loadResults();
  }

  onCategorySelect(event: any) {
    this.selectedCategory = event.target.value;
    this.loadResults();
  }

  loadResults() {
    switch (this.selectedCategory) {
      case '2025-ec-elektromobil':
        this.currentResults = this.elektromobilData;
        this.currentOrder = this.elektromobilOrder;
        break;
      case '2025-ec-hidromobil':
        this.currentResults = this.hidromobilData;
        break;
      case '2025-ec-lise':
        this.currentResults = this.liseData;
        break;
      default:
        this.currentResults = this.elektromobilData;
    }
  }

  getCategoryLabel(): string {
    const category = this.categories.find(cat => cat.value === this.selectedCategory);
    return category ? category.label : '';
  }
}
